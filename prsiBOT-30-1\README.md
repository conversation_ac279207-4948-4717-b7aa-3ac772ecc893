# CIN Extractor - Prashant

A powerful web application for extracting Corporate Identification Numbers (CIN) and company information from company names using advanced web scraping techniques.

## 🚀 Features

- **CIN Extraction**: Extract Corporate Identification Numbers from company names
- **Turnover Extraction**: Get company turnover/revenue information
- **LinkedIn Profile Extraction**: Extract LinkedIn profile data
- **Batch Processing**: Handle large CSV files with thousands of companies
- **Background Processing**: Non-blocking processing with real-time progress tracking
- **Resume Capability**: Automatic checkpointing and resume from interruptions
- **Multiple Search Engines**: Rotates between Yahoo and Bing to avoid rate limiting
- **Error Recovery**: Robust error handling with partial results download
- **History Tracking**: View and manage past extraction tasks

## 🛠️ Technology Stack

- **Backend**: Flask (Python)
- **Database**: SQLite (default) / PostgreSQL (production)
- **Web Scraping**: BeautifulSoup, Selenium WebDriver
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **Data Processing**: Pandas
- **Task Management**: Background threading with queue system

## 📋 Prerequisites

- Python 3.11 or higher
- Chrome/Chromium browser (for Selenium)
- Git

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/prashant/CIN-Extractor-prashant.git
   cd CIN-Extractor-prashant
   ```

2. **Install dependencies**
   ```bash
   pip install beautifulsoup4 email-validator flask flask-sqlalchemy gunicorn pandas psycopg2-binary requests selenium trafilatura webdriver-manager werkzeug
   ```

3. **Run the application**
   ```bash
   python main.py
   ```

4. **Access the application**
   Open your browser and go to `http://localhost:5000`

## 📖 Usage

### CIN Extraction

1. Navigate to the CIN Extractor page
2. Upload a CSV file with company names (first column should contain company names)
3. Select extraction type:
   - **CIN Only**: Extract only Corporate Identification Numbers
   - **Turnover Only**: Extract only company turnover information
   - **Both**: Extract both CIN and turnover data
4. Click "Upload and Process"
5. Monitor real-time progress
6. Download results when complete

### LinkedIn Extraction

1. Go to the LinkedIn Extractor page
2. Upload CSV with columns: "Person Name", "Current Company Name"
3. The system will search for LinkedIn profiles and extract relevant information
4. Download the enriched data

### File Format

**Input CSV format for CIN extraction:**
```csv
company_name
Reliance Industries Limited
Tata Consultancy Services
Infosys Limited
```

**Input CSV format for LinkedIn extraction:**
```csv
Person Name,Current Company Name
John Doe,Microsoft
Jane Smith,Google
```

## 🔄 Background Processing

The application uses background processing for handling large files:

- **Queue System**: Tasks are queued and processed sequentially
- **Progress Tracking**: Real-time updates on processing status
- **Checkpointing**: Automatic saves every few records
- **Resume Capability**: Continue from where you left off after interruptions
- **Error Recovery**: Download partial results even if processing fails

## 📊 Features in Detail

### Search Engine Rotation
- Automatically switches between Yahoo and Bing every 50 searches
- Implements human-like delays to avoid detection
- Uses rotating user agents for better anonymity

### Error Handling
- Comprehensive error logging
- Graceful handling of network timeouts
- Automatic retry mechanisms
- Partial results preservation

### Data Processing
- Duplicate detection and removal
- Data validation and cleaning
- Multiple output formats
- Progress persistence

## 🚀 Deployment

### Local Development
```bash
python main.py
```

### Production with Gunicorn
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Environment Variables
- `DATABASE_URL`: PostgreSQL connection string (optional)
- `FLASK_ENV`: Set to 'production' for production deployment

## 📁 Project Structure

```
CIN-Extractor-prashant/
├── app.py                 # Main Flask application
├── main.py               # Application entry point
├── models.py             # Database models
├── utils/
│   ├── scraper.py        # CIN/Turnover scraping logic
│   └── linkedin_scraper.py # LinkedIn scraping logic
├── templates/            # HTML templates
├── static/              # CSS, JS, and static assets
├── uploads/             # Uploaded CSV files
├── outputs/             # Processed results
├── checkpoints/         # Recovery checkpoints
└── instance/            # Database files
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## ⚠️ Disclaimer

This tool is for educational and research purposes. Please ensure you comply with the terms of service of the websites you're scraping and respect rate limits.

## 📞 Support

For support, please open an issue on GitHub or contact the maintainer.

---

**Made with ❤️ by Prashant**
