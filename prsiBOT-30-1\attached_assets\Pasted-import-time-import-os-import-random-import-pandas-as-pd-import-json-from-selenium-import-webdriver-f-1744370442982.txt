import time
import os
import random
import pandas as pd
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException
from datetime import datetime, timedelta

# Configurations
SEARCH_ENGINES = {"yahoo": "https://search.yahoo.com", "bing": "https://www.bing.com"}
file_path = r"C:\Users\<USER>\Downloads\LINKEDIN_EXT\OPOS_HUNGERBOX5k.csv"
checkpoint_file = r"C:\Users\<USER>\Downloads\LINKEDIN_EXT\checkpoint.json"
output_dir = r"C:\Users\<USER>\Downloads\LINKEDIN_EXT\output"

# Ensure output directory exists
os.makedirs(output_dir, exist_ok=True)

# Human-like behavior parameters
MIN_DELAY = 0
MAX_DELAY = 45  # Random delay between 0-45 seconds per extraction
SAVE_FREQUENCY = 10  # Save results every 10 records
RETRY_DELAY = 120  # 2 minutes before retrying after failure
MAX_RETRIES = 3  # Maximum number of retries per profile

def load_checkpoint():
    """Load the previous checkpoint if exists"""
    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ Error loading checkpoint: {e}")
    return {"last_index": 0, "timestamp": None}

def save_checkpoint(idx):
    """Save the current progress"""
    with open(checkpoint_file, 'w') as f:
        json.dump({
            "last_index": idx,
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }, f)

def human_delay():
    """Generate a human-like delay between operations"""
    delay = random.uniform(MIN_DELAY, MAX_DELAY)
    return delay

def setup_driver():
    """Setup Chrome driver with anti-detection measures"""
    options = Options()
    # Uncomment for headless mode if needed
    # options.add_argument("--headless")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--start-maximized")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option("useAutomationExtension", False)
    driver = webdriver.Chrome(options=options)
    # Set navigator.webdriver to false using JavaScript
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def search_linkedin(driver, name, company, retries=0):
    """Search for LinkedIn profile across multiple search engines with human-like behavior"""
    if retries >= MAX_RETRIES:
        return "Max Retries Exceeded"
    
    for engine in ["yahoo", "bing"]:
        try:
            # Navigate to search engine
            print(f"   🌐 Searching on {engine.capitalize()}...")
            driver.get(SEARCH_ENGINES[engine])
            
            # Variable delay before typing (human-like)
            time.sleep(random.uniform(1.5, 3.5))
            
            # Find search box based on engine
            search_box = driver.find_element(By.NAME, "p" if engine == "yahoo" else "q")
            
            # Clear any existing text
            search_box.clear()
            
            # Type query with random pauses between keystrokes (human-like)
            query = f"{name} {company} site:linkedin.com/in/"
            for char in query:
                search_box.send_keys(char)
                time.sleep(random.uniform(0.05, 0.25))  # Random typing speed
            
            # Slight pause before hitting Enter (human-like)
            time.sleep(random.uniform(0.5, 1.5))
            search_box.send_keys(Keys.RETURN)
            
            # Variable wait time for search results
            time.sleep(random.uniform(2, 4.5))
            
            # Scroll down slightly (human-like)
            driver.execute_script("window.scrollBy(0, 300);")
            time.sleep(random.uniform(0.5, 1.2))
            
            # Find LinkedIn result
            try:
                if engine == "yahoo":
                    results = driver.find_elements(By.CSS_SELECTOR, "div.options-toggle")
                    for i in range(min(5, len(results))):
                        link_elements = driver.find_elements(By.CSS_SELECTOR, "h3.title a")
                        for link_element in link_elements:
                            link = link_element.get_attribute("href")
                            if link and "linkedin.com/in/" in link:
                                # Hover over link before clicking (human-like)
                                return link
                else:  # bing
                    link_elements = driver.find_elements(By.CSS_SELECTOR, "li.b_algo h2 a")
                    for link_element in link_elements:
                        link = link_element.get_attribute("href")
                        if link and "linkedin.com/in/" in link:
                            return link
            except Exception as e:
                print(f"   ⚠️ Error finding links on {engine}: {str(e)}")
                continue
                
        except WebDriverException as e:
            print(f"   ⚠️ Browser error on {engine}: {str(e)}")
            # If browser crashed, restart it
            driver.quit()
            driver = setup_driver()
            
    # If we get here, we failed with both engines
    return "Not Found"

def estimate_completion_time(start_time, processed_count, total_count):
    """Estimate the remaining time based on current progress"""
    if processed_count == 0:
        return "Calculating..."
    
    elapsed_time = (datetime.now() - start_time).total_seconds()
    avg_time_per_record = elapsed_time / processed_count
    remaining_records = total_count - processed_count
    remaining_seconds = avg_time_per_record * remaining_records
    
    # Convert to human-readable format
    remaining_time = timedelta(seconds=int(remaining_seconds))
    estimated_completion = datetime.now() + remaining_time
    
    return (
        f"~{str(remaining_time).split('.')[0]} remaining | "
        f"ETA: {estimated_completion.strftime('%Y-%m-%d %H:%M:%S')}"
    )

def main():
    # Load data
    df = pd.read_csv(file_path).fillna("")
    
    # Check if LinkedIn URL and Processing Status columns exist, add if not
    if "LinkedIn URL" not in df.columns:
        df["LinkedIn URL"] = "Not Processed"
    if "Processing Status" not in df.columns:
        df["Processing Status"] = "Pending"
    
    # Load checkpoint
    checkpoint = load_checkpoint()
    start_index = checkpoint["last_index"]
    
    print(f"🚀 Starting LinkedIn profile scraper")
    print(f"📊 Total profiles to process: {len(df)}")
    
    if start_index > 0:
        print(f"⏮️ Resuming from index {start_index} (checkpoint found)")
    
    # Setup driver
    driver = setup_driver()
    
    # Track start time for estimates
    start_time = datetime.now()
    success_count = 0
    failure_count = 0
    
    try:
        for idx, row in df.iloc[start_index:].iterrows():
            # Check if this row was already processed
            if df.at[idx, "Processing Status"] != "Pending":
                print(f"⏭️ Skipping already processed row {idx + 1}")
                continue
            
            person, company = row["Person Name"].strip(), row["Current Company Name"].strip()
            
            if not person or not company:
                df.at[idx, "Processing Status"] = "Skipped - Missing Data"
                print(f"⚠️ Skipped: Missing data for row {idx + 1}")
                continue
            
            # Progress display with time estimate
            progress = f"[{idx + 1}/{len(df)}]"
            time_estimate = estimate_completion_time(start_time, idx - start_index + 1, len(df) - start_index)
            print(f"\n🔍 Now processing {progress}: {person} from {company}")
            print(f"⏱️ {time_estimate}")
            
            # Human-like delay before processing
            delay = human_delay()
            print(f"   ⏳ Waiting {delay:.2f} seconds...")
            time.sleep(delay)
            
            # Add periodic longer pauses (every 20 profiles)
            if (idx + 1) % 20 == 0 and idx > 0:
                pause_time = random.uniform(30, 60)
                print(f"😴 Taking a short break for {pause_time:.1f} seconds after {idx + 1} profiles...")
                time.sleep(pause_time)
            
            # Add more significant breaks every 100 profiles
            if (idx + 1) % 100 == 0 and idx > 0:
                print(f"💤 Taking a longer break for {RETRY_DELAY} seconds after {idx + 1} profiles...")
                time.sleep(RETRY_DELAY)
            
            # Search for LinkedIn profile with retry logic
            retry_count = 0
            linkedin_url = None
            
            while retry_count < MAX_RETRIES and linkedin_url is None:
                try:
                    linkedin_url = search_linkedin(driver, person, company, retry_count)
                    
                    if linkedin_url == "Max Retries Exceeded":
                        linkedin_url = "Failed After Multiple Attempts"
                        break
                        
                except Exception as e:
                    retry_count += 1
                    print(f"   ⚠️ Error during search (Attempt {retry_count}/{MAX_RETRIES}): {str(e)}")
                    
                    if retry_count < MAX_RETRIES:
                        print(f"   🔄 Waiting {RETRY_DELAY} seconds before retrying...")
                        time.sleep(RETRY_DELAY)
                        
                        # Restart driver if needed
                        try:
                            driver.quit()
                        except:
                            pass
                        driver = setup_driver()
            
            # Update results
            df.at[idx, "LinkedIn URL"] = linkedin_url if linkedin_url else "Error"
            
            if linkedin_url and linkedin_url != "Not Found" and linkedin_url != "Failed After Multiple Attempts":
                status = "✅ Success"
                success_count += 1
            else:
                status = "❌ Not Found" if linkedin_url == "Not Found" else "⚠️ Error"
                failure_count += 1
                
            df.at[idx, "Processing Status"] = status
            print(f"   {status} - {linkedin_url}")
            
            # Save checkpoint and results periodically
            if (idx + 1) % SAVE_FREQUENCY == 0 or idx == len(df) - 1:
                save_checkpoint(idx + 1)
                
                # Generate filename with timestamp
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_path = os.path.join(output_dir, f"linkedin_results_{timestamp}.csv")
                
                # Save current progress
                df.to_csv(output_path, index=False)
                print(f"💾 Progress saved to {output_path}")
                
                # Stats update
                processed = idx - start_index + 1
                print(f"📈 Stats: {processed} processed, {success_count} found, {failure_count} not found")
    
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
    finally:
        # Final save
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        final_output_path = os.path.join(output_dir, f"linkedin_results_FINAL_{timestamp}.csv")
        df.to_csv(final_output_path, index=False)
        print(f"✅ Final results saved to {final_output_path}")
        
        # Close driver
        try:
            driver.quit()
        except:
            pass
        
        # Print summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        print(f"\n🏁 Process completed in {duration:.2f} seconds")
        print(f"📊 Summary: {success_count} profiles found, {failure_count} not found")

if __name__ == "__main__":
    # Implement retry logic for the entire process
    max_program_retries = 3
    retry_count = 0
    
    while retry_count < max_program_retries:
        try:
            main()
            break  # If completed successfully, exit the loop
        except Exception as e:
            retry_count += 1
            print(f"\n⚠️ Program crashed: {str(e)}")
            
            if retry_count < max_program_retries:
                print(f"🔄 Waiting {RETRY_DELAY} seconds before restarting the program (Attempt {retry_count}/{max_program_retries})...")
                time.sleep(RETRY_DELAY)
            else:
                print("❌ Maximum program retries exceeded. Please check the logs.")